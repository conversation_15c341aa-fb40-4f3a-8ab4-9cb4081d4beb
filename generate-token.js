import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

const secret = process.env.JWT_SECRET;

if (!secret || secret === 'your-django-secret-key-here') {
  console.error('Error: JWT_SECRET is not defined or is set to the placeholder value in your .env file.');
  console.error('Please create a .env file in the project root and set JWT_SECRET to a secure, random string.');
  process.exit(1);
}

// Mimic the payload from your Wagtail view
const payload = {
  user_id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  is_admin: false,
  is_superuser: false,
};

// Generate the token. By default, it does not have an expiration.
// Your Django library likely adds an expiration, so we will add one too (e.g., 1 hour).
const token = jwt.sign(payload, secret, { expiresIn: '1h' });

console.log('Your test token is:\n');
console.log(token); 
import { useState } from "react";
import { <PERSON>tings, TestTube, CheckCircle, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";

interface WebhookConfigProps {
  onClose: () => void;
}

export function WebhookConfig({ onClose }: WebhookConfigProps) {
  const [webhookUrl, setWebhookUrl] = useState("");
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [isTestingWebhook, setIsTestingWebhook] = useState(false);
  const { toast } = useToast();

  const handleTestWebhook = async () => {
    if (!webhookUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter a webhook URL first",
        variant: "destructive",
      });
      return;
    }

    setIsTestingWebhook(true);
    setTestResult(null);

    try {
      const response = await fetch("/api/test-webhook", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ webhookUrl }),
      });

      const data = await response.json();
      
      setTestResult({
        success: data.success,
        message: data.success ? "Webhook test successful!" : data.error || "Webhook test failed",
      });

      if (data.success) {
        toast({
          title: "Success",
          description: "Webhook is working correctly!",
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: "Failed to test webhook: " + (error instanceof Error ? error.message : "Unknown error"),
      });
    } finally {
      setIsTestingWebhook(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Webhook Configuration
          </CardTitle>
          <CardDescription>
            Configure your n8n webhook URL to connect the AI assistant
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="webhook-url">N8N Webhook URL</Label>
            <Input
              id="webhook-url"
              type="url"
              placeholder="https://your-n8n-instance.com/webhook/your-webhook-id"
              value={webhookUrl}
              onChange={(e) => setWebhookUrl(e.target.value)}
            />
          </div>

          {testResult && (
            <Alert className={testResult.success ? "border-green-500" : "border-red-500"}>
              {testResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}
              <AlertDescription>{testResult.message}</AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button
              onClick={handleTestWebhook}
              disabled={isTestingWebhook}
              variant="outline"
              className="flex-1"
            >
              <TestTube className="h-4 w-4 mr-2" />
              {isTestingWebhook ? "Testing..." : "Test Webhook"}
            </Button>
            <Button onClick={onClose} variant="ghost">
              Close
            </Button>
          </div>

          <div className="text-sm text-muted-foreground">
            <p className="font-medium mb-1">Instructions:</p>
            <ol className="list-decimal list-inside space-y-1 text-xs">
              <li>Copy your n8n webhook URL</li>
              <li>Paste it in the field above</li>
              <li>Click "Test Webhook" to verify</li>
              <li>Update your .env file with: N8N_WEBHOOK_URL=your-url</li>
              <li>Restart the application</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
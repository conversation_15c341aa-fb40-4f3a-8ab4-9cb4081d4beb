import React, { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Outlet, useNavigate, useSearchParams } from 'react-router-dom';

const AuthGuard = () => {
  const { isAuthenticated, login, logout, isLoading } = useAuth();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    const tokenFromUrl = searchParams.get('token');

    if (tokenFromUrl) {
      // Clear the token from the URL
      searchParams.delete('token');
      setSearchParams(searchParams, { replace: true });

      // The login function now handles verification
      login(tokenFromUrl);
    } else {
      const storedToken = localStorage.getItem('authToken');
      if (storedToken) {
        login(storedToken);
      } else {
        logout();
      }
    }
  }, []); // Run only once on component mount

  if (isLoading) {
    return <div>Loading...</div>; // Or a spinner component
  }

  return isAuthenticated ? <Outlet /> : null;
};

export default AuthGuard; 
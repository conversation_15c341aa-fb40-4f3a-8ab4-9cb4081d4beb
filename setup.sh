#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

echo "Starting development setup for Jurbot..."
echo "----------------------------------------"

# 1. Copy environment file
echo "Step 1: Copying .env.dev.example to .env..."
if [ -f ".env.dev.example" ]; then
    cp .env.dev.example .env
    echo "✅ .env file created."
else
    echo "❌ Error: .env.dev.example not found."
    exit 1
fi

# 2. Start Docker containers
echo -e "\nStep 2: Building and starting Docker containers..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d --build
echo "✅ Docker containers are starting in the background."

# Wait for the main application container to be up and running
echo -e "\nWaiting for application container to initialize..."
while [ "$(docker container inspect -f '{{.State.Status}}' jurbot-app-1 2>/dev/null)" != "running" ]; do
    sleep 3
    echo -n "."
done
echo "\n✅ Application container is running."
# Add a small extra delay to ensure internal services like the server are ready
sleep 10

# NEW STEP: Install packages needed for token generation locally
echo -e "\nStep 3: Installing local dependencies for token generation..."
npm install jsonwebtoken dotenv
echo "✅ Local dependencies installed."

# 3. Run database migrations
echo -e "\nStep 4: Applying database migrations..."
docker exec jurbot-app-1 npm run db:push
echo "✅ Database migrations applied."

# 4. Generate a test token
echo -e "\nStep 5: Generating a test JWT..."
# The generate-token.js script outputs some text before the token, so we need to grab the last line.
TOKEN=$(node generate-token.js | tail -n 1)
if [ -z "$TOKEN" ]; then
    echo "❌ Error: Failed to generate token."
    exit 1
fi
echo "✅ Token generated."

# 5. Display access URL
echo -e "\n----------------------------------------"
echo "🚀 Setup complete!"
echo "----------------------------------------"
echo "Please visit this link to access the application:"
echo ""
# Note: Using http as per README, not https.
echo "http://localhost:5001/?token=${TOKEN}"
echo "" 
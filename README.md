# Jurbot - AI Chat Organizer

Jurbot is a sophisticated, full-stack chat application designed to provide a seamless interface for interacting with an external AI service. It allows users to manage and organize their conversations into folders, providing a persistent, searchable history of all interactions.

The application is built with a modern tech stack, featuring a React frontend, an Express.js backend, and a PostgreSQL database, all containerized with Docker for easy and consistent local development.

## Features

- **Rich Chat Interface**: A clean, modern UI for sending and receiving messages.
- **Folder Organization**: Group your chat sessions into collapsible folders.
- **Session Management**: Create, rename, and delete chat sessions.
- **Bulk Actions**: Select and delete multiple chat sessions at once.
- **Date Filtering**: Filter chat history by a specific date range.
- **Markdown Support**: AI responses are rendered with full markdown support.
- **Light & Dark Themes**: Switch between themes with a persistent preference.
- **Webhook Integration**: Connects to an external AI service (e.g., n8n) to power the chat logic.

## Tech Stack

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **UI Library**: shadcn/ui on top of Radix UI
- **Styling**: Tailwind CSS
- **State Management**: TanStack Query (React Query)
- **Routing**: React Router
- **Form Handling**: React Hook Form with Zod validation

### Backend
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript (ESM)
- **Database**: PostgreSQL
- **ORM**: Drizzle ORM
- **API**: RESTful API

## Architecture Overview

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized production builds
- **UI Library**: shadcn/ui components built on Radix UI primitives
- **Styling**: Tailwind CSS with custom CSS variables for theming
- **State Management**: TanStack Query (React Query) for server state management
- **Routing**: React Router for client-side routing
- **Form Handling**: React Hook Form with Zod validation

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ESM modules
- **Database**: PostgreSQL with Drizzle ORM
- **Session Management**: Built-in memory storage with interface for future database storage
- **API Design**: RESTful API with structured error handling

### Data Flow
1. **User Interaction**: User interacts with React components.
2. **State Management**: TanStack Query manages server state and caching.
3. **API Requests**: Frontend makes REST API calls to the Express backend.
4. **Database Operations**: Drizzle ORM handles database queries.
5. **Response Handling**: Data flows back through the same path with proper error handling.

## Getting Started

Follow these instructions to get the project running on your local machine for development and testing purposes.

### Prerequisites

- [Docker](https://www.docker.com/products/docker-desktop/) and Docker Compose
- [Node.js](https://nodejs.org/) (v20 or later)
- [npm](https://www.npmjs.com/)

### Installation & Setup

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/adechristanto/jurbot.git
    cd jurbot
    ```

2.  **Create an environment file:**
    Create a `.env` file in the root of the project by copying the `.env.example` file. This file contains all the necessary environment variables for the application.
    ```bash
    cp .env.example .env
    ```
    Then, fill in the values in the `.env` file with your specific configuration, including a secure `JWT_SECRET` and strong database credentials.

3.  **Launch the application:**
    Run the following command to build the Docker images and start the application and database containers.
    ```bash
    docker-compose up --build
    ```
    The application will be available at [http://localhost:5001](http://localhost:5001).

4.  **Run Database Migrations:**
    Once the containers are running, open a **new terminal window** and execute the following command to apply the database schema:
    ```bash
    docker exec jurbot-app-1 npm run db:push
    ```

The application is now fully set up and ready to use.

## Running the Application

This project is configured to run in two modes: **local development** and **production**.

### Local Development Mode

This mode is ideal for active development. It uses the Vite development server with Hot-Module-Replacement (HMR), so changes to your code are reflected in the browser instantly without needing to restart the container.

**1. Start the development server:**
```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d --build
```

**2. Generate a Test Token:**
Because you are not running the Wagtail backend locally, you need to generate a test JWT to access the application. Run the following command in your terminal:
```bash
node generate-token.js
```
This will print a long token string to your console.

**3. Access the Application:**
Copy the token and paste it into the following URL to open the application:
```
http://localhost:5001/?token=PASTE_TOKEN_HERE
```

In this mode:
- The app is accessible at `http://localhost:5001`.
- The `logout` function redirects to a local test page (`/login-redirect-loop-test`) to prevent accidental redirects to the live site.

### Production Mode

This mode simulates the live production environment. It builds the optimized frontend and backend code and runs it using the production Node.js server. This is useful for final testing before deployment.

**To run in production mode, use this command:**
```bash
docker-compose up -d --build
```
In this mode:
- The app is accessible at `http://localhost:5001`.
- The `logout` function redirects to the live site specified by `VITE_LOGOUT_URL` in your `.env` file.
- The `JWT_SECRET` in your `.env` file **must** match the secret key used by your live Wagtail/Django application for authentication to work.

## Deployment Strategy

### Build Process
1. **Frontend Build**: Vite builds the React application to `dist/public`
2. **Backend Build**: esbuild bundles the Express server to `dist/index.js`
3. **Database Migration**: Drizzle handles schema migrations

### Environment Configuration
- **Development**: Uses Vite dev server with Express backend
- **Production**: Serves static files from Express with built frontend
- **Database**: Requires `DATABASE_URL` environment variable

## Available Scripts

- `npm run dev`: Starts the development server with hot reloading.
- `npm run build`: Creates a production build of the frontend and backend.
- `npm run start`: Starts the production server.
- `npm run db:push`: Pushes schema changes to the database using Drizzle Kit.
- `npm run check`: Runs the TypeScript compiler to check for type errors.

## Changelog
- July 05, 2025. Initial setup - Complete chat application with folder organization
- July 05, 2025. Added environment variable support for webhook configuration
- July 05, 2025. Improved webhook error handling and debugging
- July 05, 2025. Added webhook configuration UI component
- July 05, 2025. Enhanced webhook debugging with comprehensive logging and troubleshooting
- July 05, 2025. Major UI/UX improvements:
  * Implemented yellow-based color scheme with navy blue complementary colors
  * Added comprehensive light/dark theme support with theme toggle
  * Fixed markdown rendering for AI messages with proper styling
  * Improved text overflow handling with better chat bubble constraints
  * Moved settings button to sidebar bottom with theme toggle
  * Added date filtering functionality in sidebar
  * Implemented mobile-responsive input behavior (Enter for new line)
  * Removed session ID displays and "Connected" indicator for cleaner UI
  * Added working download functionality for chat sessions
  * Enhanced color contrast and accessibility across all themes 
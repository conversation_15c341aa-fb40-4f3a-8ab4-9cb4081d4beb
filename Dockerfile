# Use an official Node.js runtime as a parent image
FROM node:20-alpine AS base

# Set the working directory
WORKDIR /usr/src/app

# Install server-side dependencies
COPY package*.json ./
RUN npm install

# Install client-side dependencies
COPY client/package*.json ./client/
RUN npm install --prefix client

# Copy the rest of the application's source code
COPY . .

# Build the app
RUN npm run build

# Expose the port the app runs on
EXPOSE 5000

# Define the command to run the app
CMD [ "npm", "run", "start" ] 
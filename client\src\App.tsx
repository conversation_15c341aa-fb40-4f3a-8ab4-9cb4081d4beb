import { useEffect } from "react";
import { Routes, Route, useLocation } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/hooks/use-theme";
import Chat from "@/pages/chat";
import NotFound from "@/pages/not-found";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { AuthProvider } from "./contexts/AuthContext";
import AuthGuard from "./components/AuthGuard";
import MatomoProvider from "./contexts/MatomoProvider";
import { useMatomo } from "@datapunt/matomo-tracker-react";

const PageTracker = () => {
  const { trackPageView } = useMatomo();
  const location = useLocation();

  useEffect(() => {
    trackPageView({});
  }, [location]);

  return null;
};

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <MatomoProvider>
        <AuthProvider>
          <ThemeProvider defaultTheme="light" storageKey="chat-theme">
            <TooltipProvider>
              <Toaster />
              <PageTracker />
              <Routes>
                <Route element={<AuthGuard />}>
                  <Route path="/" element={<Chat />} />
                </Route>
                <Route path="*" element={<NotFound />} />
              </Routes>
            </TooltipProvider>
          </ThemeProvider>
        </AuthProvider>
      </MatomoProvider>
    </QueryClientProvider>
  );
}

export default App;

import { folders, chatSessions, messages, type Folder, type ChatSession, type Message, type InsertFolder, type InsertChatSession, type InsertMessage, type UpdateChatSession } from "@shared/schema";
import { db } from "./db";
import { eq } from "drizzle-orm";

export interface IStorage {
  // Folders
  getFolders(): Promise<Folder[]>;
  createFolder(folder: InsertFolder): Promise<Folder>;
  updateFolder(id: number, folder: Partial<InsertFolder>): Promise<Folder | undefined>;
  deleteFolder(id: number): Promise<boolean>;
  
  // Chat Sessions
  getChatSessions(): Promise<ChatSession[]>;
  getChatSession(id: string): Promise<ChatSession | undefined>;
  createChatSession(session: InsertChatSession): Promise<ChatSession>;
  updateChatSession(id: string, session: UpdateChatSession): Promise<ChatSession | undefined>;
  deleteChatSession(id: string): Promise<boolean>;
  moveChatSessionToFolder(sessionId: string, folderId: number | null): Promise<boolean>;
  
  // Messages
  getMessages(sessionId: string): Promise<Message[]>;
  createMessage(message: InsertMessage): Promise<Message>;
  deleteMessages(sessionId: string): Promise<boolean>;
}

export class MemStorage implements IStorage {
  private folders: Map<number, Folder>;
  private chatSessions: Map<string, ChatSession>;
  private messages: Map<string, Message[]>;
  private currentFolderId: number;
  private currentMessageId: number;

  constructor() {
    this.folders = new Map();
    this.chatSessions = new Map();
    this.messages = new Map();
    this.currentFolderId = 1;
    this.currentMessageId = 1;
  }

  async getFolders(): Promise<Folder[]> {
    return Array.from(this.folders.values()).sort((a, b) => 
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
  }

  async createFolder(insertFolder: InsertFolder): Promise<Folder> {
    const id = this.currentFolderId++;
    const folder: Folder = {
      ...insertFolder,
      id,
      createdAt: new Date(),
    };
    this.folders.set(id, folder);
    return folder;
  }

  async updateFolder(id: number, folder: Partial<InsertFolder>): Promise<Folder | undefined> {
    const existingFolder = this.folders.get(id);
    if (!existingFolder) return undefined;
    
    const updatedFolder = { ...existingFolder, ...folder };
    this.folders.set(id, updatedFolder);
    return updatedFolder;
  }

  async deleteFolder(id: number): Promise<boolean> {
    // Move all chat sessions in this folder to uncategorized
    const sessions = Array.from(this.chatSessions.values());
    for (const session of sessions) {
      if (session.folderId === id) {
        session.folderId = null;
      }
    }
    
    return this.folders.delete(id);
  }

  async getChatSessions(): Promise<ChatSession[]> {
    const sessions = Array.from(this.chatSessions.values());
    return sessions.sort((a, b) => 
      new Date(b.lastMessageAt).getTime() - new Date(a.lastMessageAt).getTime()
    );
  }

  async getChatSession(id: string): Promise<ChatSession | undefined> {
    return this.chatSessions.get(id);
  }

  async createChatSession(insertSession: InsertChatSession): Promise<ChatSession> {
    const session: ChatSession = {
      id: insertSession.id,
      title: insertSession.title,
      folderId: insertSession.folderId ?? null,
      messageCount: 0,
      lastMessageAt: new Date(),
      createdAt: new Date(),
    };
    this.chatSessions.set(session.id, session);
    return session;
  }

  async updateChatSession(id: string, updateSession: UpdateChatSession): Promise<ChatSession | undefined> {
    const existingSession = this.chatSessions.get(id);
    if (!existingSession) return undefined;
    
    const updatedSession = { ...existingSession, ...updateSession };
    this.chatSessions.set(id, updatedSession);
    return updatedSession;
  }

  async deleteChatSession(id: string): Promise<boolean> {
    this.messages.delete(id);
    return this.chatSessions.delete(id);
  }

  async moveChatSessionToFolder(sessionId: string, folderId: number | null): Promise<boolean> {
    const session = this.chatSessions.get(sessionId);
    if (!session) return false;
    
    session.folderId = folderId;
    this.chatSessions.set(sessionId, session);
    return true;
  }

  async getMessages(sessionId: string): Promise<Message[]> {
    return this.messages.get(sessionId) || [];
  }

  async createMessage(insertMessage: InsertMessage): Promise<Message> {
    const message: Message = {
      ...insertMessage,
      id: this.currentMessageId++,
      timestamp: new Date(),
    };
    
    const sessionMessages = this.messages.get(insertMessage.sessionId) || [];
    sessionMessages.push(message);
    this.messages.set(insertMessage.sessionId, sessionMessages);
    
    // Update session message count and last message time
    const session = this.chatSessions.get(insertMessage.sessionId);
    if (session) {
      session.messageCount = sessionMessages.length;
      session.lastMessageAt = new Date();
      this.chatSessions.set(insertMessage.sessionId, session);
    }
    
    return message;
  }

  async deleteMessages(sessionId: string): Promise<boolean> {
    this.messages.delete(sessionId);
    
    // Update session message count
    const session = this.chatSessions.get(sessionId);
    if (session) {
      session.messageCount = 0;
      this.chatSessions.set(sessionId, session);
    }
    
    return true;
  }
}

// DatabaseStorage implementation using Drizzle ORM
export class DatabaseStorage implements IStorage {
  async getFolders(): Promise<Folder[]> {
    return await db.select().from(folders).orderBy(folders.createdAt);
  }

  async createFolder(insertFolder: InsertFolder): Promise<Folder> {
    const [folder] = await db
      .insert(folders)
      .values(insertFolder)
      .returning();
    return folder;
  }

  async updateFolder(id: number, folder: Partial<InsertFolder>): Promise<Folder | undefined> {
    const [updatedFolder] = await db
      .update(folders)
      .set(folder)
      .where(eq(folders.id, id))
      .returning();
    return updatedFolder || undefined;
  }

  async deleteFolder(id: number): Promise<boolean> {
    // Move all chat sessions in this folder to uncategorized
    await db
      .update(chatSessions)
      .set({ folderId: null })
      .where(eq(chatSessions.folderId, id));
    
    const result = await db.delete(folders).where(eq(folders.id, id));
    return (result.rowCount || 0) > 0;
  }

  async getChatSessions(): Promise<ChatSession[]> {
    return await db.select().from(chatSessions).orderBy(chatSessions.lastMessageAt);
  }

  async getChatSession(id: string): Promise<ChatSession | undefined> {
    const [session] = await db.select().from(chatSessions).where(eq(chatSessions.id, id));
    return session || undefined;
  }

  async createChatSession(insertSession: InsertChatSession): Promise<ChatSession> {
    const [session] = await db
      .insert(chatSessions)
      .values({
        ...insertSession,
        folderId: insertSession.folderId ?? null,
      })
      .returning();
    return session;
  }

  async updateChatSession(id: string, updateSession: UpdateChatSession): Promise<ChatSession | undefined> {
    const [session] = await db
      .update(chatSessions)
      .set(updateSession)
      .where(eq(chatSessions.id, id))
      .returning();
    return session || undefined;
  }

  async deleteChatSession(id: string): Promise<boolean> {
    // Delete all messages first
    await db.delete(messages).where(eq(messages.sessionId, id));
    
    // Delete the session
    const result = await db.delete(chatSessions).where(eq(chatSessions.id, id));
    return (result.rowCount || 0) > 0;
  }

  async moveChatSessionToFolder(sessionId: string, folderId: number | null): Promise<boolean> {
    const [session] = await db
      .update(chatSessions)
      .set({ folderId })
      .where(eq(chatSessions.id, sessionId))
      .returning();
    return !!session;
  }

  async getMessages(sessionId: string): Promise<Message[]> {
    return await db.select().from(messages)
      .where(eq(messages.sessionId, sessionId))
      .orderBy(messages.timestamp);
  }

  async createMessage(insertMessage: InsertMessage): Promise<Message> {
    const [message] = await db
      .insert(messages)
      .values(insertMessage)
      .returning();
    
    // Update session message count and last message time
    const messageCount = await db.select().from(messages)
      .where(eq(messages.sessionId, insertMessage.sessionId));
    
    await db
      .update(chatSessions)
      .set({ 
        messageCount: messageCount.length,
        lastMessageAt: new Date()
      })
      .where(eq(chatSessions.id, insertMessage.sessionId));
    
    return message;
  }

  async deleteMessages(sessionId: string): Promise<boolean> {
    const result = await db.delete(messages).where(eq(messages.sessionId, sessionId));
    
    // Update session message count
    await db
      .update(chatSessions)
      .set({ messageCount: 0 })
      .where(eq(chatSessions.id, sessionId));
    
    return (result.rowCount || 0) > 0;
  }
}

// Use in-memory storage for now to get app running
export const storage = new DatabaseStorage();

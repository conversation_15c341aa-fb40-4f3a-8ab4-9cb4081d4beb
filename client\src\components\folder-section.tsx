import { useState } from "react";
import { Folder as FolderI<PERSON>, MessageCircle, Edit3, Trash2, GripVertical, MoreVertical } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ChatSessionItem } from "@/components/chat-session-item";
import { useFolders } from "@/hooks/use-folders";
import { useChat } from "@/hooks/use-chat";
import type { Folder, ChatSession } from "@shared/schema";

interface FolderSectionProps {
  folder: Folder | null;
  sessions: ChatSession[];
  currentSessionId: string | null;
  selectedChats: string[];
  onSelectChat: (sessionId: string) => void;
  onToggleSelect: (sessionId: string) => void;
}

export function FolderSection({
  folder,
  sessions,
  currentSessionId,
  selectedChats,
  onSelectChat,
  onToggleSelect,
}: FolderSectionProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const { updateFolder, deleteFolder } = useFolders();
  const { moveSession, deleteSession } = useChat();

  const handleEditFolder = async () => {
    if (!folder) return;
    const newName = prompt("Enter new folder name:", folder.name);
    if (newName && newName !== folder.name) {
      await updateFolder(folder.id, { name: newName });
    }
  };

  const handleDeleteFolder = async () => {
    if (!folder) return;
    if (confirm(`Are you sure you want to delete the folder "${folder.name}"? Chat sessions will be moved to uncategorized.`)) {
      await deleteFolder(folder.id);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const sessionId = e.dataTransfer.getData("text/plain");
    if (sessionId) {
      await moveSession(sessionId, folder?.id || null);
    }
  };

  const folderName = folder ? folder.name : "Uncategorized";
  const ComponentIcon = folder ? FolderIcon : MessageCircle;

  return (
    <div className="folder-section">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <ComponentIcon className={`h-4 w-4 ${folder ? "text-primary" : "text-muted-foreground"}`} />
          <span className={`font-medium ${folder ? "text-foreground" : "text-muted-foreground"}`}>
            {folderName}
          </span>
          <span className="text-xs text-muted-foreground">({sessions.length})</span>
        </div>
        {folder && (
          <div className="flex gap-1">
            <Button
              onClick={handleEditFolder}
              size="sm"
              variant="ghost"
              className="p-1 h-auto text-muted-foreground hover:text-foreground"
            >
              <Edit3 className="h-3 w-3" />
            </Button>
            <Button
              onClick={handleDeleteFolder}
              size="sm"
              variant="ghost"
              className="p-1 h-auto text-muted-foreground hover:text-foreground"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        )}
      </div>

      <div
        className={`space-y-2 border-2 border-dashed rounded-lg p-2 transition-all duration-200 ${
          isDragOver
            ? "border-primary bg-primary/10"
            : "border-transparent hover:border-border"
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {sessions.map((session) => (
          <ChatSessionItem
            key={session.id}
            session={session}
            isSelected={selectedChats.includes(session.id)}
            isCurrent={currentSessionId === session.id}
            onSelect={onSelectChat}
            onToggleSelect={onToggleSelect}
          />
        ))}
      </div>
    </div>
  );
}

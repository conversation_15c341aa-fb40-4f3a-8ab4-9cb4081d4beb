import { MatomoProvider as Provider, createInstance } from '@datapunt/matomo-tracker-react';
import React, { useMemo } from 'react';

const MatomoProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const instance = useMemo(() => {
    return createInstance({
      urlBase: 'https://tracking.strafrecht-online.org',
      siteId: 2,
    });
  }, []);

  return <Provider value={instance}>{children}</Provider>;
};

export default MatomoProvider; 